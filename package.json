{"name": "mediapilot", "version": "1.0.0", "engines": {"node": ">=20.18.0", "npm": ">=10.8.2"}, "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint-fix": "next lint --fix", "test": "CI=true jest --env=jsdom", "svg-to-react": "svgr assets/icons --out-dir common/components/icons --icon --typescript --prettier-config .prettierrc"}, "dependencies": {"@ai-sdk/anthropic": "^0.0.51", "@headlessui/react": "^1.7.18", "@hookform/resolvers": "^3.9.0", "@next/font": "^14.2.15", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "@react-spring/three": "^9.7.5", "@react-three/drei": "^9.115.0", "@react-three/fiber": "^8.17.10", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.47.2", "@tabler/icons-react": "^3.19.0", "@tanstack/react-query": "^5.59.14", "@tiptap/extension-color": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-list-item": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@tomfreudenberg/next-auth-mock": "^0.5.6", "@types/fabric": "^5.3.10", "@types/negotiator": "^0.6.3", "@types/node": "20.11.30", "@types/react-dom": "^18.2.22", "ai": "^3.4.18", "autoprefixer": "10.4.19", "autosize": "^6.0.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "debounce-promise": "^3.1.2", "embla-carousel": "^8.3.1", "embla-carousel-react": "^8.3.1", "eslint-config-next": "^14.1.4", "ethers": "^6.13.2", "fabric": "^6.7.0", "framer-motion": "^11.11.8", "imagetracerjs": "^1.2.6", "lucide-react": "^0.452.0", "mixpanel-browser": "^2.56.0", "motion": "^12.7.3", "negotiator": "^0.6.3", "next": "^14.1.4", "next-auth": "^4.24.7", "node-forge": "^1.3.1", "pinata-web3": "^0.5.1", "postcss": "^8.4.38", "react": "18.2.0", "react-dom": "18.2.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-lottie": "^1.2.4", "react-select": "^5.8.1", "regenerator-runtime": "^0.14.1", "sass": "^1.72.0", "stripe": "^18.0.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.3", "tailwindcss": "3.4.1", "three": "^0.170.0", "typescript": "^5.4.3", "viem": "^2.21.26", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@svgr/cli": "^8.1.0", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.2", "@testing-library/user-event": "^14.5.2", "@types/autosize": "^4.0.3", "@types/debounce-promise": "^3.1.9", "@types/jest": "^29.5.12", "@types/mixpanel-browser": "^2.50.2", "@types/node-forge": "^1.3.11", "@types/react": "^18.2.67", "@types/react-lottie": "^1.2.10", "@types/three": "^0.169.0", "@types/uuid": "^10.0.0", "@types/whatwg-fetch": "^0.0.33", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "^3.6.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.2.10", "ts-node": "^10.9.2", "undici": "^5.27.2", "whatwg-fetch": "^3.6.20"}, "msw": {"workerDirectory": "public"}}