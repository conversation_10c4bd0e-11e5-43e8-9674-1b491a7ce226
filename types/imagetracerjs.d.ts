declare module 'imagetracerjs' {
  interface TracingOptions {
    ltres?: number;
    qtres?: number;
    pathomit?: number;
    colorsampling?: number;
    numberofcolors?: number;
    mincolorratio?: number;
    colorquantcycles?: number;
    layering?: number;
    strokewidth?: number;
    linefilter?: boolean;
    scale?: number;
    roundcoords?: number;
    viewbox?: boolean;
    desc?: boolean;
    lcpr?: number;
    qcpr?: number;
    blurradius?: number;
    blurdelta?: number;
  }

  interface ImageTracer {
    imagedataToSVG(imagedata: ImageData, options?: TracingOptions): string;
    imageToSVG(image: HTMLImageElement, options?: TracingOptions): string;
    appendSVGString(svgstring: string, parentid: string): void;
  }

  const ImageTracer: ImageTracer;
  export default ImageTracer;
}
