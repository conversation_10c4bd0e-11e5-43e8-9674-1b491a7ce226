import { createClient } from '@/common/utils/supabase/client';
import { SupabaseTables } from '@/common/constants';

export interface ImageMetadata {
  id: string;
  projectId: string;
  agentId: string;
  imageType: 'generated' | 'uploaded' | 'creations';
  filePath: string;
  fileName: string;
  planId?: string;
  description?: string;
  createdAt: string;
}

export interface ProjectImageStorage {
  generated: ImageMetadata[];
  uploaded: ImageMetadata[];
  creations: ImageMetadata[];
}

const MAX_IMAGES_PER_TYPE = 20;

export class ProjectImageStorageManager {
  private supabase = createClient();

  private async getProjectImages (projectId: string): Promise<ProjectImageStorage> {
    try {
      const { 
        data, error,
      } = await this.supabase
        .from(SupabaseTables.Projects)
        .select('images')
        .eq('project_id', projectId)
        .single();

      if (error) {
        console.error('Error fetching project images:', error);
        return {
          generated: [],
          uploaded: [],
          creations: [],
        };
      }

      if (data?.images) {
        const parsedImages = JSON.parse(data.images);
        return {
          generated: parsedImages.generated || [],
          uploaded: parsedImages.uploaded || [],
          creations: parsedImages.creations || [],
        };
      }
    } catch (error) {
      console.error('Error parsing stored project images:', error);
    }

    return {
      generated: [],
      uploaded: [],
      creations: [],
    };
  }

  private async saveProjectImages (projectId: string, images: ProjectImageStorage): Promise<void> {
    try {
      const { error } = await this.supabase
        .from(SupabaseTables.Projects)
        .update({ images: JSON.stringify(images) })
        .eq('project_id', projectId);

      if (error) {
        console.error('Error saving project images:', error);
      }
    } catch (error) {
      console.error('Error saving project images:', error);
    }
  }

  async addGeneratedImage (
    projectId: string,
    agentId: string,
    filePath: string,
    fileName: string,
    planId?: string,
    description?: string,
  ): Promise<void> {
    const images = await this.getProjectImages(projectId);

    const newImage: ImageMetadata = {
      id: `gen_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      projectId,
      agentId,
      imageType: 'generated',
      filePath,
      fileName,
      planId,
      description,
      createdAt: new Date().toISOString(),
    };

    images.generated.unshift(newImage);

    // Keep only the most recent images
    if (images.generated.length > MAX_IMAGES_PER_TYPE) {
      images.generated = images.generated.slice(0, MAX_IMAGES_PER_TYPE);
    }

    await this.saveProjectImages(projectId, images);
  }

  async addUploadedImage (
    projectId: string,
    agentId: string,
    filePath: string,
    fileName: string,
    planId?: string,
  ): Promise<void> {
    const images = await this.getProjectImages(projectId);

    const newImage: ImageMetadata = {
      id: `upl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      projectId,
      agentId,
      imageType: 'uploaded',
      filePath,
      fileName,
      planId,
      createdAt: new Date().toISOString(),
    };

    images.uploaded.unshift(newImage);

    if (images.uploaded.length > MAX_IMAGES_PER_TYPE) {
      images.uploaded = images.uploaded.slice(0, MAX_IMAGES_PER_TYPE);
    }

    await this.saveProjectImages(projectId, images);
  }

  async addCreationImage (
    projectId: string,
    agentId: string,
    filePath: string,
    fileName: string,
    planId?: string,
    description?: string,
  ): Promise<void> {
    const images = await this.getProjectImages(projectId);

    const newImage: ImageMetadata = {
      id: `crt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      projectId,
      agentId,
      imageType: 'creations',
      filePath,
      fileName,
      planId,
      description,
      createdAt: new Date().toISOString(),
    };

    images.creations.unshift(newImage);

    if (images.creations.length > MAX_IMAGES_PER_TYPE) {
      images.creations = images.creations.slice(0, MAX_IMAGES_PER_TYPE);
    }

    await this.saveProjectImages(projectId, images);
  }

  async getRecentImages (projectId: string): Promise<ProjectImageStorage> {
    return await this.getProjectImages(projectId);
  }

  async clearProjectImages (projectId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from(SupabaseTables.Projects)
        .update({ images: JSON.stringify({
          generated: [],
          uploaded: [],
          creations: [],
        }) })
        .eq('project_id', projectId);

      if (error) {
        console.error('Error clearing project images:', error);
      }
    } catch (error) {
      console.error('Error clearing project images:', error);
    }
  }
  
}

// Export a singleton instance
export const projectImageStorage = new ProjectImageStorageManager();
