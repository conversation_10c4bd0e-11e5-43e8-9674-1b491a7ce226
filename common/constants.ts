import lang from "./lang";

const {
  manageIdea,
} = lang;

export const mobileWidthLimit = 480;
export const tabletWidthLimit = 768;
export const lowResDeskLimit = 1024;
export const highResDeskLimit = 1280;

export const FILE_SIZE_10_MB = 10000000;
export const acceptedImageMimeTypes = [
  "image/jpeg",
  "image/png",
  "image/svg+xml",
  "image/webp",
  "image/gif",
  "image/apng",
  "image/avif",
];
export const agentLoadingStates = manageIdea.promptLoadingStates;

export const emailRegex =
  /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const usernameRegex = /^[A-Za-z0-9_]{4,15}$/;

export const SupabaseTables = {
  Projects: process.env.NEXT_PUBLIC_TABLE_MEDIA_PROJECTS || '',
};

// Platform character limits
export const PLATFORM_CHARACTER_LIMITS = {
  TWITTER: 280,
  TWITTER_PREMIUM: 25000,
  LINKEDIN: 3000,
  INSTAGRAM: 2200,
  FACEBOOK: 63206,
  YOUTUBE: 5000,
} as const;

export const PLATFORM_CANVAS_SIZES = {
  twitter: { 
    width: 1080, 
    height: 1080,
  },
  x: { 
    width: 1080, 
    height: 1080,
  },
  instagram: { 
    width: 1080, 
    height: 1350,
  },
  linkedin: { 
    width: 1200, 
    height: 1200,
  },
  facebook: { 
    width: 1200, 
    height: 628,
  },
  youtube: { 
    width: 1280, 
    height: 720,
  },
  default: { 
    width: 1200, 
    height: 1200,
  },
} as const;

export const tones = [
  "Professional",
  "Gen-Z",
  "Casual",
  "Academic",
  "Mentor",
  "Creative",
];

export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const isProd = process.env.NEXT_PUBLIC_ROOT_DOMAIN === "mediapilot.app";

export const VectorImageStyleGroups = {
  none: {
    title: "No Style",
    styles: [
      {
        option: "none",
        label: "No Style",
      },
    ],
  },
  logos: {
    title: "Logos & Icons",
    styles: [
      {
        option: "minimalistLogo",
        label: "Minimalist Logo",
      },
      {
        option: "geometricLogo",
        label: "Geometric Logo",
      },
      {
        option: "flatDesign",
        label: "Flat Design",
      },
      {
        option: "iconDesign",
        label: "Icon Design",
      },
      {
        option: "badge",
        label: "Badge",
      },
      {
        option: "emblem",
        label: "Emblem",
      },
    ],
  },
  illustrations: {
    title: "Vector Illustrations",
    styles: [
      {
        option: "vectorIllustration",
        label: "Vector Illustration",
      },
      {
        option: "lineArt",
        label: "Line Art",
      },
      {
        option: "silhouette",
        label: "Silhouette",
      },
      {
        option: "stencilArt",
        label: "Stencil Art",
      },
      {
        option: "technicalIllustration",
        label: "Technical Illustration",
      },
      {
        option: "isometricDrawing",
        label: "Isometric Drawing",
      },
    ],
  },
  geometric: {
    title: "Geometric & Abstract",
    styles: [
      {
        option: "geometricAbstraction",
        label: "Geometric Abstraction",
      },
      {
        option: "mandala",
        label: "Mandala",
      },
      {
        option: "kaleidoscopic",
        label: "Kaleidoscopic",
      },
      {
        option: "cubism",
        label: "Cubism",
      },
    ],
  },
};

export const ImageStyleGroups = {
  none: {
    title: "No Style",
    styles: [
      {
        option: "none",
        label: "No Style",
      },
    ],
  },
  genres: {
    title: "Genres & Themes",
    styles: [
      {
        option: "fantasy",
        label: "Fantasy",
      },
      {
        option: "sciFi",
        label: "Sci-Fi",
      },
      {
        option: "horror",
        label: "Horror",
      },
      {
        option: "cyberpunk",
        label: "Cyberpunk",
      },
      {
        option: "steampunk",
        label: "Steampunk",
      },
      {
        option: "matrix",
        label: "Matrix",
      },
      {
        option: "gameOfThrones",
        label: "Game of Thrones",
      },
      {
        option: "pixar",
        label: "Pixar",
      },
      {
        option: "skyrim",
        label: "Skyrim",
      },
      {
        option: "tokusatsu",
        label: "Tokusatsu",
      },
      {
        option: "noir",
        label: "Noir",
      },
      {
        option: "vaporwave",
        label: "Vaporwave",
      },
      {
        option: "darkSynth",
        label: "Dark Synth",
      },
      {
        option: "baroque",
        label: "Baroque",
      },
      {
        option: "gothicArt",
        label: "Gothic Art",
      },
    ],
  },
  photography: {
    title: "Photography Styles",
    styles: [
      {
        option: "candidPhotography",
        label: "Candid Photography",
      },
      {
        option: "streetPhotography",
        label: "Street Photography",
      },
      {
        option: "cinematicFilmStill",
        label: "Cinematic Film Still",
      },
      {
        option: "amateurPhotography",
        label: "Amateur Photography",
      },
      {
        option: "portraitPhotography",
        label: "Portrait Photography",
      },
      {
        option: "glamourPhotography",
        label: "Glamour Photography",
      },
      {
        option: "beautyPhotography",
        label: "Beauty Photography",
      },
      {
        option: "fashionPhotography",
        label: "Fashion Photography",
      },
      {
        option: "highkeyPhotography",
        label: "Highkey Photography",
      },
      {
        option: "lowkeyPhotography",
        label: "Lowkey Photography",
      },
      {
        option: "landscapePhotography",
        label: "Landscape Photography",
      },
      {
        option: "sportsPhotography",
        label: "Sports Photography",
      },
      {
        option: "infraredPhotography",
        label: "Infrared Photography",
      },
      {
        option: "dreamlikePhotography",
        label: "Dreamlike Photography",
      },
      {
        option: "filmNegative",
        label: "Film Negative",
      },
      {
        option: "infrared",
        label: "Infrared",
      },
      {
        option: "splitToning",
        label: "Split Toning",
      },
      {
        option: "tiltShiftPhotography",
        label: "Tilt-Shift Photography",
      },
      {
        option: "nightPhotography",
        label: "Night Photography",
      },
      {
        option: "flashPhotography",
        label: "Flash Photography",
      },
      {
        option: "chiaroscuroPhotography",
        label: "Chiaroscuro Photography",
      },
      {
        option: "minimalistPhotography",
        label: "Minimalist Photography",
      },
      {
        option: "surrealistPhotography",
        label: "Surrealist Photography",
      },
      {
        option: "abstractPhotography",
        label: "Abstract Photography",
      },
      {
        option: "fantasyPhotography",
        label: "Fantasy Photography",
      },
      {
        option: "loFiPhotography",
        label: "Lo-Fi Photography",
      },
      {
        option: "analoguePhotography",
        label: "Analogue Photography",
      },
      {
        option: "silhouettePhotography",
        label: "Silhouette Photography",
      },
      {
        option: "underwaterPhotography",
        label: "Underwater Photography",
      },
      {
        option: "photomontage",
        label: "Photomontage",
      },
    ],
  },
  mediums: {
    title: "Mediums & Techniques",
    styles: [
      {
        option: "filmStill",
        label: "Film Still",
      },
      {
        option: "nikonD850",
        label: "Nikon D850",
      },
      {
        option: "pixarRender",
        label: "Pixar Render",
      },
      {
        option: "hannaBarberaCartoon",
        label: "Hanna Barbera Cartoon",
      },
      {
        option: "digitalPainting",
        label: "Digital Painting",
      },
      {
        option: "charcoalInkwash",
        label: "Charcoal Inkwash",
      },
      {
        option: "watercolor",
        label: "Watercolor",
      },
      {
        option: "encaustic",
        label: "Encaustic",
      },
      {
        option: "impasto",
        label: "Impasto",
      },
      {
        option: "ebru",
        label: "Ebru",
      },
      {
        option: "risograph",
        label: "Risograph",
      },
      {
        option: "claymation",
        label: "Claymation",
      },
    ],
  },
  classic: {
    title: "Classic & Historical",
    styles: [
      {
        option: "vintagePosterArt",
        label: "Vintage Poster Art",
      },
      {
        option: "artDeco",
        label: "Art Deco",
      },
      {
        option: "artNouveau",
        label: "Art Nouveau",
      },
      {
        option: "chromolithography",
        label: "Chromolithography",
      },
      {
        option: "fengShui",
        label: "Feng Shui",
      },
      {
        option: "tenebrism",
        label: "Tenebrism",
      },
      {
        option: "ukiyoE",
        label: "Ukiyo-e",
      },
      {
        option: "abstractExpressionism",
        label: "Abstract Expressionism",
      },
      {
        option: "cubism",
        label: "Cubism",
      },
      {
        option: "symbolism",
        label: "Symbolism",
      },
      {
        option: "synthetism",
        label: "Synthetism",
      },
      {
        option: "dadaism",
        label: "Dadaism",
      },
      {
        option: "deconstructivism",
        label: "Deconstructivism",
      },
      {
        option: "geometricAbstraction",
        label: "Geometric Abstraction",
      },
      {
        option: "impressionism",
        label: "Impressionism",
      },
      {
        option: "minimalism",
        label: "Minimalism",
      },
      {
        option: "minimalistLandscape",
        label: "Minimalist Landscape",
      },
      {
        option: "pentimento",
        label: "Pentimento",
      },
      {
        option: "surrealism",
        label: "Surrealism",
      },
    ],
  },
  digital: {
    title: "Digital & Modern",
    styles: [
      {
        option: "eightBit",
        label: "8-Bit",
      },
      {
        option: "animatedGifArt",
        label: "Animated GIF Art",
      },
      {
        option: "diffusion",
        label: "Diffusion",
      },
      {
        option: "digitalAbstract",
        label: "Digital Abstract",
      },
      {
        option: "vectorArt",
        label: "Vector Art",
      },
      {
        option: "barcodeArtwork",
        label: "Barcode Artwork",
      },
      {
        option: "futurism",
        label: "Futurism",
      },
      {
        option: "hyperrealism",
        label: "Hyperrealism",
      },
      {
        option: "magicRealism",
        label: "Magic Realism",
      },
      {
        option: "popArt",
        label: "Pop Art",
      },
      {
        option: "vectorIllustration",
        label: "Vector Illustration",
      },
      {
        option: "greebles",
        label: "Greebles",
      },
      {
        option: "infographicIllustration",
        label: "Infographic Illustration",
      },
      {
        option: "lowPoly",
        label: "Low Poly",
      },
      {
        option: "pixelated",
        label: "Pixelated",
      },
      {
        option: "popSurrealism",
        label: "Pop Surrealism",
      },
      {
        option: "visionaryArt",
        label: "Visionary Art",
      },
    ],
  },
  illustration: {
    title: "Illustration & Drawing",
    styles: [
      {
        option: "editorialIllustration",
        label: "Editorial Illustration",
      },
      {
        option: "chibi",
        label: "Chibi",
      },
      {
        option: "doodle",
        label: "Doodle",
      },
      {
        option: "technicalIllustration",
        label: "Technical Illustration",
      },
      {
        option: "typographyArt",
        label: "Typography Art",
      },
      {
        option: "blueprint",
        label: "Blueprint",
      },
      {
        option: "crossHatching",
        label: "Cross Hatching",
      },
      {
        option: "dotMatrixArt",
        label: "Dot Matrix Art",
      },
      {
        option: "engraving",
        label: "Engraving",
      },
      {
        option: "calligraphy",
        label: "Calligraphy",
      },
      {
        option: "pointillism",
        label: "Pointillism",
      },
      {
        option: "rorschachInkblot",
        label: "Rorschach Inkblot",
      },
      {
        option: "stencilArt",
        label: "Stencil Art",
      },
      {
        option: "zentangle",
        label: "Zentangle",
      },
      {
        option: "artBrut",
        label: "Art Brut",
      },
      {
        option: "experimentalTypography",
        label: "Experimental Typography",
      },
      {
        option: "etchASketch",
        label: "Etch-A-Sketch",
      },
      {
        option: "isometricDrawing",
        label: "Isometric Drawing",
      },
      {
        option: "linocutPrint",
        label: "Linocut Print",
      },
      {
        option: "monochromeArt",
        label: "Monochrome Art",
      },
    ],
  },
  decorative: {
    title: "Decorative & Collage",
    styles: [
      {
        option: "aboriginalDotPainting",
        label: "Aboriginal Dot Painting",
      },
      {
        option: "grisaille",
        label: "Grisaille",
      },
      {
        option: "kaleidoscopic",
        label: "Kaleidoscopic",
      },
      {
        option: "kineticArt",
        label: "Kinetic Art",
      },
      {
        option: "mandala",
        label: "Mandala",
      },
      {
        option: "mosaic",
        label: "Mosaic",
      },
      {
        option: "neuronFlowers",
        label: "Neuron Flowers",
      },
      {
        option: "prismaArt",
        label: "Prisma Art",
      },
      {
        option: "sgraffito",
        label: "Sgraffito",
      },
      {
        option: "tarotCards",
        label: "Tarot Cards",
      },
      {
        option: "collageArt",
        label: "Collage Art",
      },
      {
        option: "alebrijes",
        label: "Alebrijes",
      },
      {
        option: "anthotype",
        label: "Anthotype",
      },
      {
        option: "arabesque",
        label: "Arabesque",
      },
      {
        option: "batik",
        label: "Batik",
      },
      {
        option: "decorativeArts",
        label: "Decorative Arts",
      },
      {
        option: "enameled",
        label: "Enameled",
      },
      {
        option: "fauxFinish",
        label: "Faux Finish",
      },
      {
        option: "fractal",
        label: "Fractal",
      },
      {
        option: "generativeDesign",
        label: "Generative Design",
      },
      {
        option: "gothicRevival",
        label: "Gothic Revival",
      },
      {
        option: "knolling",
        label: "Knolling",
      },
      {
        option: "mixedMedia",
        label: "Mixed Media",
      },
      {
        option: "surrealCollage",
        label: "Surreal Collage",
      },
      {
        option: "tornPaperCollage",
        label: "Torn Paper Collage",
      },
      {
        option: "trashArt",
        label: "Trash Art",
      },
    ],
  },
  organic: {
    title: "Nature-Inspired & Organic",
    styles: [
      {
        option: "bacteriaArt",
        label: "Bacteria Art",
      },
      {
        option: "cymatics",
        label: "Cymatics",
      },
      {
        option: "dmtArtStyle",
        label: "DMT Art Style",
      },
      {
        option: "inkblot",
        label: "Inkblot",
      },
      {
        option: "liquidChrome",
        label: "Liquid Chrome",
      },
      {
        option: "prismatic",
        label: "Prismatic",
      },
      {
        option: "sculpturalArt",
        label: "Sculptural Art",
      },
      {
        option: "wabiSabi",
        label: "Wabi-Sabi",
      },
      {
        option: "glassmorphism",
        label: "Glassmorphism",
      },
      {
        option: "kirigami",
        label: "Kirigami",
      },
      {
        option: "ferrofluid",
        label: "Ferrofluid",
      },
      {
        option: "quillingArtwork",
        label: "Quilling Artwork",
      },
      {
        option: "feltArt",
        label: "Felt Art",
      },
      {
        option: "textileArt",
        label: "Textile Art",
      },
      {
        option: "yarnArt",
        label: "Yarn Art",
      },
    ],
  },
  optical: {
    title: "Optical & Photographic Effects",
    styles: [
      {
        option: "diorama",
        label: "Diorama",
      },
      {
        option: "opticalIllusion",
        label: "Optical Illusion",
      },
      {
        option: "shadowArt",
        label: "Shadow Art",
      },
      {
        option: "stereogram",
        label: "Stereogram",
      },
      {
        option: "trompeLoeil",
        label: "Trompe L'oeil",
      },
      {
        option: "anamorphicArt",
        label: "Anamorphic Art",
      },
    ],
  },
};

// Flatten grouped styles for backward compatibility

export const tonesSelection = [
  {
    title: "👨 Professional",
    value: "Professional",
  },
  {
    title: "🧑‍💻 Gen-Z",
    value: "Gen-Z",
  },
  {
    title: "🤙 Casual",
    value: "Casual",
  },
  {
    title: "👨‍🏫 Academic",
    value: "Academic",
  },
  {
    title: "🧑‍🏫 Mentor",
    value: "Mentor",
  },
  {
    title: "👨‍🎨 Creative",
    value: "Creative",
  },
];

export const basicPlanLink = 'https://buy.stripe.com/test_fZeg342659J6cqQ3cc'

export const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'text/markdown',
  'text/plain',
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024;
