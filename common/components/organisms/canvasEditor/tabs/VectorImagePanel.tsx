'use client'

import React, { 
  useEffect, useRef, useState,
} from 'react';
import * as fabric from 'fabric';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import {
  ArrowLeft, Download, Eye,
} from 'lucide-react';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';

// Import the image tracer library
import ImageTracer from 'imagetracerjs';

interface VectorImagePanelProps {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
}

interface TracingOptions {
  ltres: number;        // Line threshold
  qtres: number;        // Quad threshold  
  pathomit: number;     // Path omit
  colorsampling: number; // Color sampling
  numberofcolors: number; // Number of colors
  mincolorratio: number; // Min color ratio
  colorquantcycles: number; // Color quantization cycles
  blurradius: number;   // Blur radius
  blurdelta: number;    // Blur delta
}

const defaultTracingOptions: TracingOptions = {
  ltres: 1,
  qtres: 1,
  pathomit: 8,
  colorsampling: 1,
  numberofcolors: 16,
  mincolorratio: 0.02,
  colorquantcycles: 3,
  blurradius: 0,
  blurdelta: 20,
};

export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
  containerRef,
  zoomLevel,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof VectorImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isTracing, setIsTracing] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details' | 'trace'>('style');
  const [generatedImageUrl, setGeneratedImageUrl] = useState<string | null>(null);
  const [tracingOptions, setTracingOptions] = useState<TracingOptions>(defaultTracingOptions);
  const [svgPreview, setSvgPreview] = useState<string | null>(null);
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof VectorImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleBackToDetails = () => {
    setCurrentStep('details');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const requestBody: any = {
        description: imagePrompt,
        planId: planId || 'new-post',
        seed: seed,
        guidanceScale: guidanceScale,
      };

      if (selectedStyle && selectedStyle.option !== 'none') {
        requestBody.style = [selectedStyle.option];
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate image');
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath) {
        const imageUrl = getPath(imageData.filepath);
        setGeneratedImageUrl(imageUrl);
        setCurrentStep('trace');

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Image generated! Now configure tracing settings.');
      } else {
        throw new Error('No image data received');
      }
    } catch (error: any) {
      console.error('Error generating image:', error);
      setError(error.message || 'Failed to generate image. Please try again.');
      toast.error(error.message || 'Failed to generate image');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTrace = async () => {
    if (!generatedImageUrl) {
      setError('No image to trace');
      return;
    }

    setIsTracing(true);
    setError('');

    try {
      // Load the image and convert to canvas for tracing
      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = generatedImageUrl;
      });

      // Create a temporary canvas to get image data
      const tempCanvas = document.createElement('canvas');
      const ctx = tempCanvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      tempCanvas.width = img.width;
      tempCanvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Get image data for tracing
      const imageData = ctx.getImageData(0, 0, img.width, img.height);

      // Trace the image to SVG
      const svgString = ImageTracer.imagedataToSVG(imageData, tracingOptions);
      setSvgPreview(svgString);

      toast.success('Image traced to vector format!');
    } catch (error: any) {
      console.error('Error tracing image:', error);
      setError(error.message || 'Failed to trace image');
      toast.error('Failed to trace image');
    } finally {
      setIsTracing(false);
    }
  };

  const handleSaveToCanvas = async () => {
    if (!svgPreview || !canvas) {
      return;
    }

    try {
      // Create a blob from the SVG string
      const svgBlob = new Blob([svgPreview], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      // Load SVG into fabric canvas
      fabric.loadSVGFromURL(svgUrl).then((result: any) => {
        const { objects, options } = result;
        const svgGroup = fabric.util.groupSVGElements(objects, options);
        
        // Scale to fit canvas if needed
        if (canvas && containerRef?.current) {
          const containerRect = containerRef.current.getBoundingClientRect();
          const maxWidth = (containerRect.width * 0.8) / (zoomLevel || 1);
          const maxHeight = (containerRect.height * 0.8) / (zoomLevel || 1);
          
          if (svgGroup.width! > maxWidth || svgGroup.height! > maxHeight) {
            const scale = Math.min(maxWidth / svgGroup.width!, maxHeight / svgGroup.height!);
            svgGroup.scale(scale);
          }
        }

        canvas.add(svgGroup);
        canvas.centerObject(svgGroup);
        canvas.setActiveObject(svgGroup);
        canvas.renderAll();

        // Clean up the blob URL
        URL.revokeObjectURL(svgUrl);

        // Store in project images
        if (activeProject?.project_id && agentId) {
          const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            svgUrl,
            fileName,
            planId,
            imagePrompt,
          ).then(() => {
            window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
          }).catch((error) => {
            console.error('Error storing vector image:', error);
          });
        }

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image added to canvas!');
      });
    } catch (error: any) {
      console.error('Error saving to canvas:', error);
      toast.error('Failed to save vector image');
    }
  };

  const downloadSVG = () => {
    if (!svgPreview) return;

    const blob = new Blob([svgPreview], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vector-image-${Date.now()}.svg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector images using AI with SVG output</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 text-left ${
                            selectedStyle?.option === style.option
                              ? 'border-violets-are-blue bg-violets-are-blue/10'
                              : 'border-neutral-600 hover:border-violets-are-blue bg-neutral-800 hover:bg-neutral-700'
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {style.label}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4 flex items-center gap-3">
            <Button
              onClick={handleBackToStyles}
              variant="outline"
              size="sm"
              className="!px-2"
            >
              <ArrowLeft size={16} />
            </Button>
            <div>
              <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
              <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
              </p>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Image Description
              </label>
              <TextArea
                id="vector-image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                className="w-full min-h-[100px] resize-none"
                maxLength={500}
              />
              <div className="flex justify-between items-center mt-1">
                <span className="text-xs text-gray-500">
                  {imagePrompt.length}/500 characters
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Seed: {seed}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1000000"
                  step="1"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Guidance: {guidanceScale}
                </label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  step="0.1"
                  value={guidanceScale}
                  onChange={(e) => setGuidanceScale(Number(e.target.value))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={isGenerating || !imagePrompt.trim()}
            >
              {isGenerating ? 'Generating...' : 'Generate Vector Image'}
            </Button>
          </div>
        </>
      ) : currentStep === 'trace' ? (
        <>
          <div className="mb-4 flex items-center gap-3">
            <Button
              onClick={handleBackToDetails}
              variant="outline"
              size="sm"
              className="!px-2"
            >
              <ArrowLeft size={16} />
            </Button>
            <div>
              <h3 className="text-white font-semibold text-lg">Vectorize Image</h3>
              <p className="text-gray-400 text-sm">Configure tracing settings and preview</p>
            </div>
          </div>

          <div className="space-y-6">
            {generatedImageUrl && (
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Generated Image
                </label>
                <div className="bg-neutral-800 rounded-lg p-3 border border-neutral-700">
                  <Image
                    src={generatedImageUrl}
                    alt="Generated image"
                    width={200}
                    height={200}
                    className="w-full h-auto rounded-lg"
                  />
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Colors: {tracingOptions.numberofcolors}
                </label>
                <input
                  type="range"
                  min="2"
                  max="64"
                  step="1"
                  value={tracingOptions.numberofcolors}
                  onChange={(e) => setTracingOptions(prev => ({ ...prev, numberofcolors: Number(e.target.value) }))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Detail: {tracingOptions.ltres}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="10"
                  step="0.1"
                  value={tracingOptions.ltres}
                  onChange={(e) => setTracingOptions(prev => ({ ...prev, ltres: Number(e.target.value) }))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Smoothing: {tracingOptions.qtres}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="10"
                  step="0.1"
                  value={tracingOptions.qtres}
                  onChange={(e) => setTracingOptions(prev => ({ ...prev, qtres: Number(e.target.value) }))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Path Simplify: {tracingOptions.pathomit}
                </label>
                <input
                  type="range"
                  min="0"
                  max="50"
                  step="1"
                  value={tracingOptions.pathomit}
                  onChange={(e) => setTracingOptions(prev => ({ ...prev, pathomit: Number(e.target.value) }))}
                  className="w-full accent-violets-are-blue"
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <div className="flex gap-3">
              <Button
                variant="outline"
                size="md"
                onClick={handleTrace}
                disabled={isTracing || !generatedImageUrl}
                className="flex-1"
              >
                <Eye size={16} className="mr-2" />
                {isTracing ? 'Tracing...' : 'Preview Vector'}
              </Button>
            </div>

            {svgPreview && (
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Vector Preview
                </label>
                <div className="bg-neutral-800 rounded-lg p-3 border border-neutral-700">
                  <div
                    className="w-full max-h-64 overflow-auto bg-white rounded"
                    dangerouslySetInnerHTML={{ __html: svgPreview }}
                  />
                </div>
                <div className="flex gap-3 mt-3">
                  <Button
                    variant="gradient"
                    size="md"
                    onClick={handleSaveToCanvas}
                    className="flex-1"
                  >
                    Add to Canvas
                  </Button>
                  <Button
                    variant="outline"
                    size="md"
                    onClick={downloadSVG}
                  >
                    <Download size={16} className="mr-2" />
                    Download SVG
                  </Button>
                </div>
              </div>
            )}
          </div>
        </>
      ) : null}
    </div>
  );
};
